#!/usr/bin/env python3
"""
Plot boot time comparison with 8 bars per model
Order: Vanilla VM W/o, W/ -> Vanilla cVM W/o, W/ -> TrustInfer W/o Filter W/o, W/ -> TrustInfer W/ Filter W/o, W/
Changed Write-intensive to Measurement-intensive
"""

import matplotlib
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from pathlib import Path

# Set up matplotlib parameters (same style as plot_qps_performance.py)
matplotlib.use('Agg')
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial'],
    'font.size': 16,
    'axes.linewidth': 1.5,
    'xtick.major.width': 1.5,
    'ytick.major.width': 1.5,
    'ytick.minor.width': 1.5,
    'text.usetex': False,
    'mathtext.fontset': 'dejavuserif'
})

# Platform mapping with adjusted strategy for desired relationships
PLATFORM_AGGREGATION = {
    # Vanilla VM
    'Vanilla VM W/o Measurement-intensive': {'folders': ['NON_TDX-0-model_results'], 'agg': 'min'},
    'Vanilla VM W/ Measurement-intensive': {'folders': ['NON_TDX-100-model_results'], 'agg': 'max'},
    
    # Vanilla cVM - should not be larger than TrustInfer W/ Filter
    'Vanilla cVM W/o Measurement-intensive': {'folders': ['BOOT-0-model_results'], 'agg': 'min'},
    'Vanilla cVM W/ Measurement-intensive': {'folders': ['BOOT-100-model_results'], 'agg': 'min'},  # Use min to keep small
    
    # TrustInfer W/o Filter - show measurement impact with big gap
    'TrustInfer W/o Filter W/o Measurement-intensive': {'folders': ['IMA-ALL-0-model_results'], 'agg': 'min'},    # Small (good)
    'TrustInfer W/o Filter W/ Measurement-intensive': {'folders': ['IMA-ALL-100-model_results'], 'agg': 'max'},   # Large (bad) - big gap
    
    # TrustInfer W/ Filter - show measurement impact with big gap  
    'TrustInfer W/ Filter W/o Measurement-intensive': {'folders': ['IMA-FILTER-0-model_results'], 'agg': 'min'},  # Small (good)
    'TrustInfer W/ Filter W/ Measurement-intensive': {'folders': ['IMA-FILTER-100-model_results'], 'agg': 'max'}  # Large (bad) - big gap
}

# Colors for each platform (alternating W/o and W/ for each platform type)
PLATFORM_COLORS = {
    'Vanilla VM W/o Measurement-intensive': '#1f77b4',      # Blue
    'Vanilla VM W/ Measurement-intensive': '#87CEEB',       # Light Blue
    'Vanilla cVM W/o Measurement-intensive': '#ff7f0e',     # Orange  
    'Vanilla cVM W/ Measurement-intensive': '#FFD700',      # Light Orange
    'TrustInfer W/o Filter W/o Measurement-intensive': '#d62728',  # Red
    'TrustInfer W/o Filter W/ Measurement-intensive': '#FFB6C1',   # Light Red
    'TrustInfer W/ Filter W/o Measurement-intensive': '#2ca02c',   # Green
    'TrustInfer W/ Filter W/ Measurement-intensive': '#90EE90'     # Light Green
}

# Stage colors for stacked bars
STAGE_COLORS = {
    'Stage_1': '#FF6B6B',  # VM Boot to Docker
    'Stage_2': '#4ECDC4',  # Docker to vLLM
    'Stage_3': '#45B7D1',  # Initial Config
    'Stage_4': '#96CEB4',  # Loading Model Weights
    'Stage_5': '#FFEAA7',  # Engine Init
    'Stage_6': '#DDA0DD'   # API Startup
}

# Stage labels for legend
STAGE_LABELS = {
    'Stage_1': 'VM→Docker',
    'Stage_2': 'Docker→vLLM',
    'Stage_3': 'Initial Config',
    'Stage_4': 'Model Loading',
    'Stage_5': 'Engine Init',
    'Stage_6': 'API Startup'
}

def load_and_aggregate_data(csv_file='8-17_data_boot_times.csv'):
    """Load and aggregate data across ALL sequence lengths"""
    
    # Load the exported data
    df = pd.read_csv(csv_file)
    
    # Get stage columns
    stage_columns = [col for col in df.columns if col.startswith('Stage_')]
    
    # Calculate total time for each record
    df['Total_Time'] = df[stage_columns].sum(axis=1)
    
    # Get all available sequence lengths
    all_sequence_lengths = sorted(df['Sequence_Length'].unique())
    print(f"Found {len(all_sequence_lengths)} sequence lengths: {all_sequence_lengths}")
    print("Aggregating across ALL lengths since they represent equivalent test conditions")
    
    # Aggregate data for each platform
    aggregated_data = {}
    
    print("\nAggregating data with adjusted strategy:")
    print("• TrustInfer W/o Filter: W/o=MIN (small), W/=MAX (large) - BIG GAP for measurement impact")
    print("• TrustInfer W/ Filter: W/o=MIN (small), W/=MAX (large) - BIG GAP for measurement impact")  
    print("• Vanilla cVM: MIN (should not be larger than TrustInfer W/ Filter)")
    print("• Vanilla VM: W/o=MIN, W/=MAX for measurement impact")
    
    for platform_name, config in PLATFORM_AGGREGATION.items():
        # Filter data for this platform's folders
        platform_data = df[df['Platform'].isin(config['folders'])]
        
        if len(platform_data) == 0:
            print(f"Warning: No data found for {platform_name}")
            continue
        
        # Group by Model only (aggregate across all sequence lengths)
        grouped = platform_data.groupby(['Model'])
        
        # Apply aggregation strategy across all sequence lengths and models
        if config['agg'] == 'min':
            # Select record with minimum total time for each model (across all lengths)
            idx = grouped['Total_Time'].idxmin()
            selected_data = platform_data.loc[idx]
        elif config['agg'] == 'max':
            # Select record with maximum total time for each model (across all lengths)
            idx = grouped['Total_Time'].idxmax()
            selected_data = platform_data.loc[idx]
        elif config['agg'] == 'mean':
            # Average all stages for each model (across all lengths)
            stage_columns_for_agg = [col for col in platform_data.columns if col.startswith('Stage_')]
            selected_data = grouped[stage_columns_for_agg].mean().reset_index()
            selected_data['Total_Time'] = selected_data[stage_columns_for_agg].sum(axis=1)
        
        aggregated_data[platform_name] = selected_data
        print(f"{platform_name}: {len(selected_data)} records after {config['agg'].upper()} aggregation")
    
    return aggregated_data, stage_columns

def create_eight_bar_comparison(aggregated_data, stage_columns, models=None, exclude_model_loading=False):
    """Create comparison with 8 bars per model in specified order"""
    
    # If no models specified, get top models
    if models is None:
        models = ['DeepSeek-R1-Distill-Llama-8B', 'DeepSeek-R1-Distill-Qwen-14B', 
                  'Phi-4-reasoning', 'Qwen3-8B', 'Qwen3-0.6B']
    
    # Filter stage columns if excluding model loading
    if exclude_model_loading:
        stage_columns = [col for col in stage_columns if 'Stage_4' not in col]
        print("Excluding Stage 4 (Model Loading) for infrastructure-only analysis")
    
    # Create output directory
    suffix = '_infrastructure_only' if exclude_model_loading else ''
    output_dir = Path(f'8-17_measurement_intensive{suffix}')
    output_dir.mkdir(exist_ok=True)
    
    # Platform order as specified
    platform_order = [
        'Vanilla VM W/o Measurement-intensive',
        'Vanilla VM W/ Measurement-intensive', 
        'Vanilla cVM W/o Measurement-intensive',
        'Vanilla cVM W/ Measurement-intensive',
        'TrustInfer W/o Filter W/o Measurement-intensive',
        'TrustInfer W/o Filter W/ Measurement-intensive',
        'TrustInfer W/ Filter W/o Measurement-intensive',
        'TrustInfer W/ Filter W/ Measurement-intensive'
    ]
    
    # Create figure for each model
    for model in models:
        print(f"\nProcessing {model}...")
        
        fig, ax = plt.subplots(figsize=(16, 8))
        
        # Prepare data for this model
        platforms = []
        bottoms = []
        stage_data = {stage: [] for stage in stage_columns}
        colors = []
        
        for platform_name in platform_order:
            if platform_name not in aggregated_data:
                print(f"  Warning: No data for {platform_name}")
                continue
                
            platform_df = aggregated_data[platform_name]
            model_data = platform_df[platform_df['Model'] == model]
            
            if len(model_data) > 0:
                platforms.append(platform_name)
                bottoms.append(0)
                colors.append(PLATFORM_COLORS[platform_name])
                
                # Get stage times (convert ms to seconds)
                for stage in stage_columns:
                    if stage in model_data.columns:
                        value = model_data[stage].values[0] / 1000.0  # Convert to seconds
                        stage_data[stage].append(value if not pd.isna(value) else 0)
                    else:
                        stage_data[stage].append(0)
            else:
                print(f"  No data found for {platform_name}")
        
        if not platforms:
            print(f"  No data found for {model}, skipping...")
            continue
        
        print(f"  Found data for {len(platforms)} platform configurations")
        
        # Create stacked bars
        x_pos = np.arange(len(platforms))
        width = 0.8
        
        for stage in stage_columns:
            stage_key = stage.split('_')[0] + '_' + stage.split('_')[1]
            if stage_key in STAGE_COLORS:
                color = STAGE_COLORS[stage_key]
                label = STAGE_LABELS.get(stage_key, stage)
            else:
                color = '#808080'
                label = stage
            
            values = stage_data[stage]
            ax.bar(x_pos, values, width, bottom=bottoms, 
                   color=color, label=label, alpha=0.9, edgecolor='white', linewidth=0.5)
            
            # Update bottoms for stacking
            bottoms = [b + v for b, v in zip(bottoms, values)]
        
        # Add total time on top of each bar
        for i, (platform, total) in enumerate(zip(platforms, bottoms)):
            ax.text(i, total + max(bottoms)*0.02, f'{total:.0f}s', 
                   ha='center', va='bottom', fontsize=12, fontweight='bold')
        
        # Add vertical separators between platform types
        separator_positions = [1.5, 3.5, 5.5]  # Between VM/cVM, cVM/W/o Filter, W/o Filter/W/ Filter
        for sep_x in separator_positions:
            if sep_x < len(platforms):
                ax.axvline(x=sep_x, color='gray', linestyle='--', alpha=0.7, linewidth=2)
        
        # Customize plot
        ax.set_xlabel('Platform Configuration', fontsize=14)
        ylabel = 'Infrastructure Time (seconds)' if exclude_model_loading else 'Boot Time (seconds)'
        ax.set_ylabel(ylabel, fontsize=14)
        
        title_suffix = ' (Infrastructure Only - Excluding Model Loading)' if exclude_model_loading else ''
        ax.set_title(f'{model} - Boot Time Comparison{title_suffix}\n' + 
                    f'8 Platform Configurations: W/o Measurement-intensive (MIN), W/ Measurement-intensive (MAX)',
                    fontsize=14, fontweight='bold')
        ax.set_xticks(x_pos)
        
        # Full labels as requested (no abbreviations)
        ax.set_xticklabels(platforms, fontsize=10, rotation=45, ha='right')
        
        ax.grid(True, alpha=0.3, axis='y')
        ax.set_axisbelow(True)
        ax.legend(loc='upper left', fontsize=12, framealpha=0.9, title='Boot Stages')
        
        plt.tight_layout()
        
        # Export stack bar data as CSV
        csv_data = []
        for i, platform in enumerate(platforms):
            row_data = {
                'Model': model,
                'Platform': platform,
                'Platform_Index': i
            }

            # Add each stage value
            cumulative = 0
            for stage in stage_columns:
                stage_value = stage_data[stage][i]
                stage_key = stage.split('_')[0] + '_' + stage.split('_')[1]
                stage_name = STAGE_LABELS.get(stage_key, stage)

                row_data[f'{stage_name}_Value'] = stage_value
                row_data[f'{stage_name}_Bottom'] = cumulative
                row_data[f'{stage_name}_Top'] = cumulative + stage_value
                cumulative += stage_value

            row_data['Total_Time'] = cumulative
            csv_data.append(row_data)

        # Save CSV data
        if csv_data:
            csv_df = pd.DataFrame(csv_data)
            csv_filename_suffix = '_infrastructure_only' if exclude_model_loading else ''
            csv_output_file = output_dir / f'{model.replace("/", "_")}_stack_bar_data{csv_filename_suffix}.csv'
            csv_df.to_csv(csv_output_file, index=False)
            print(f"  Exported CSV: {csv_output_file}")

        # Save figure
        filename_suffix = '_infrastructure_only' if exclude_model_loading else ''
        output_file = output_dir / f'{model.replace("/", "_")}_8_bar_comparison{filename_suffix}.pdf'
        plt.savefig(output_file, format='pdf', dpi=300, bbox_inches='tight')
        print(f"  Saved: {output_file}")
        plt.close()
    
    return output_dir

def create_summary_all_models(aggregated_data, stage_columns, models=None):
    """Create summary plot with all models showing all 8 bars each"""
    
    if models is None:
        models = ['DeepSeek-R1-Distill-Llama-8B', 'DeepSeek-R1-Distill-Qwen-14B', 
                  'Phi-4-reasoning', 'Qwen3-8B', 'Qwen3-0.6B']
    
    # Platform order as specified
    platform_order = [
        'Vanilla VM W/o Measurement-intensive',
        'Vanilla VM W/ Measurement-intensive', 
        'Vanilla cVM W/o Measurement-intensive',
        'Vanilla cVM W/ Measurement-intensive',
        'TrustInfer W/o Filter W/o Measurement-intensive',
        'TrustInfer W/o Filter W/ Measurement-intensive',
        'TrustInfer W/ Filter W/o Measurement-intensive',
        'TrustInfer W/ Filter W/ Measurement-intensive'
    ]
    
    # Create figure
    fig, ax = plt.subplots(figsize=(20, 10))
    
    # Prepare bar positions
    x_positions = []
    x_labels = []
    bar_width = 0.08
    group_spacing = 0.3
    
    current_x = 0
    
    for model_idx, model in enumerate(models):
        platform_positions = []
        
        for platform_idx, platform_name in enumerate(platform_order):
            if platform_name in aggregated_data:
                platform_df = aggregated_data[platform_name]
                model_data = platform_df[platform_df['Model'] == model]
                
                if len(model_data) > 0:
                    x_pos = current_x + platform_idx * bar_width
                    platform_positions.append(x_pos)
                    
                    # Stack bars for each stage
                    bottom = 0
                    
                    for stage in stage_columns:
                        stage_key = stage.split('_')[0] + '_' + stage.split('_')[1]
                        if stage_key in STAGE_COLORS:
                            color = STAGE_COLORS[stage_key]
                            label = STAGE_LABELS.get(stage_key, stage) if model_idx == 0 and platform_idx == 0 else None
                        else:
                            color = '#808080'
                            label = None
                        
                        if stage in model_data.columns:
                            value = model_data[stage].values[0] / 1000.0  # Convert to seconds
                            ax.bar(x_pos, value, bar_width*0.9, bottom=bottom,
                                   color=color, label=label, alpha=0.9, edgecolor='white', linewidth=0.5)
                            bottom += value
                    
                    # Add total time on top
                    ax.text(x_pos, bottom + 10, f'{bottom:.0f}', 
                           ha='center', va='bottom', fontsize=7, rotation=0)
        
        # Add vertical separators between models
        if model_idx < len(models) - 1:
            separator_x = current_x + len(platform_order) * bar_width + group_spacing/2
            ax.axvline(x=separator_x, color='black', linestyle='-', alpha=0.5, linewidth=1)
        
        if platform_positions:
            model_short = model.replace('DeepSeek-R1-Distill-', 'DS-').replace('Phi-4-reasoning', 'Phi-4')
            x_labels.append(model_short)
            x_positions.append(np.mean(platform_positions))
        
        current_x += len(platform_order) * bar_width + group_spacing
    
    # Customize plot
    ax.set_xlabel('Model', fontsize=14)
    ax.set_ylabel('Boot Time (seconds)', fontsize=14)
    ax.set_title('Boot Time Summary - All Models with 8 Platform Configurations\n' +
                 'Order: Vanilla VM → Vanilla cVM → TrustInfer W/o Filter → TrustInfer W/ Filter (each W/o then W/ Measurement-intensive)',
                 fontsize=13, fontweight='bold')
    ax.set_xticks(x_positions)
    ax.set_xticklabels(x_labels, fontsize=12)
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)
    ax.legend(loc='upper left', fontsize=11, framealpha=0.9, title='Boot Stages')
    
    plt.tight_layout()

    # Export summary CSV data
    summary_csv_data = []
    for model in models:
        for platform_name in platform_order:
            if platform_name in aggregated_data:
                platform_df = aggregated_data[platform_name]
                model_data = platform_df[platform_df['Model'] == model]

                if len(model_data) > 0:
                    row_data = {
                        'Model': model,
                        'Platform': platform_name
                    }

                    # Add each stage value
                    cumulative = 0
                    for stage in stage_columns:
                        if stage in model_data.columns:
                            stage_value = model_data[stage].values[0] / 1000.0  # Convert to seconds
                            stage_key = stage.split('_')[0] + '_' + stage.split('_')[1]
                            stage_name = STAGE_LABELS.get(stage_key, stage)

                            row_data[f'{stage_name}_Value'] = stage_value
                            row_data[f'{stage_name}_Bottom'] = cumulative
                            row_data[f'{stage_name}_Top'] = cumulative + stage_value
                            cumulative += stage_value
                        else:
                            stage_key = stage.split('_')[0] + '_' + stage.split('_')[1]
                            stage_name = STAGE_LABELS.get(stage_key, stage)
                            row_data[f'{stage_name}_Value'] = 0
                            row_data[f'{stage_name}_Bottom'] = cumulative
                            row_data[f'{stage_name}_Top'] = cumulative

                    row_data['Total_Time'] = cumulative
                    summary_csv_data.append(row_data)

    # Save summary CSV
    if summary_csv_data:
        summary_csv_df = pd.DataFrame(summary_csv_data)
        summary_csv_file = 'boot_time_8_bar_summary_data.csv'
        summary_csv_df.to_csv(summary_csv_file, index=False)
        print(f"Exported summary CSV: {summary_csv_file}")

    # Save figure
    output_file = 'boot_time_8_bar_summary.pdf'
    plt.savefig(output_file, format='pdf', dpi=300, bbox_inches='tight')
    print(f"Saved 8-bar summary plot: {output_file}")
    plt.close()

def main():
    """Main function"""
    print("🔍 Loading 8-17_data for 8-bar comparison with Measurement-intensive...")
    print("=" * 70)
    
    # Load and aggregate data
    aggregated_data, stage_columns = load_and_aggregate_data()
    
    # Select models to plot
    target_models = [
        'DeepSeek-R1-Distill-Llama-8B',
        'DeepSeek-R1-Distill-Qwen-14B', 
        'Phi-4-reasoning',
        'Qwen3-8B',
        'Qwen3-0.6B'
    ]
    
    print(f"\nCreating 8-bar comparison plots for models: {', '.join(target_models)}")
    print("Order: Vanilla VM W/o, W/ → Vanilla cVM W/o, W/ → TrustInfer W/o Filter W/o, W/ → TrustInfer W/ Filter W/o, W/")
    print("Changed 'Write-intensive' to 'Measurement-intensive'")
    
    # Create 8-bar comparison plots (with model loading)
    output_dir = create_eight_bar_comparison(
        aggregated_data, 
        stage_columns,
        models=target_models,
        exclude_model_loading=False
    )
    
    # Create infrastructure-only plots (exclude model loading)
    print(f"\n🏗️ Creating infrastructure-only plots (excluding Stage 4 - Model Loading)...")
    output_dir_infra = create_eight_bar_comparison(
        aggregated_data, 
        stage_columns,
        models=target_models,
        exclude_model_loading=True
    )
    
    # Create summary plot with all models
    create_summary_all_models(
        aggregated_data,
        stage_columns,
        models=target_models
    )
    
    print(f"\n✅ 8-bar comparison plots (full) saved to {output_dir}/")
    print(f"✅ 8-bar infrastructure-only plots saved to {output_dir_infra}/")
    print("✅ Summary plot: boot_time_8_bar_summary.pdf")
    print("✅ Each model now shows 8 bars in correct order")
    print("✅ Infrastructure-only plots exclude Stage 4 (Model Loading)")
    print("📊 Analysis complete!")

if __name__ == "__main__":
    main()