#!/usr/bin/env python3
"""
Export CDF data to CSV files for plot_8_17_measurement_intensive.py

This script extracts the raw data used to generate the CDF charts and exports it
as CSV files that can be used with other plotting scripts.
"""

import pandas as pd
import numpy as np
import glob
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def extract_model_name(full_model_name):
    """Extract a clean model name from the full model path."""
    # Handle different naming patterns from different platforms
    if 'Qwen3-8B' in full_model_name or 'Qwen_Qwen3-8B' in full_model_name:
        return 'Qwen3-8B'
    elif 'Qwen3-0.6B' in full_model_name or 'Qwen_Qwen3-0.6B' in full_model_name:
        return 'Qwen3-0.6B'
    elif 'DeepSeek-R1-Distill-Llama-8B' in full_model_name or 'deepseek-ai_DeepSeek-R1-Distill-Llama-8B' in full_model_name:
        return 'DeepSeek-R1-Llama-8B'
    elif 'DeepSeek-R1-Distill-Qwen-14B' in full_model_name or 'deepseek-ai_DeepSeek-R1-Distill-Qwen-14B' in full_model_name:
        return 'DeepSeek-R1-Qwen-14B'
    elif 'Phi-4-reasoning' in full_model_name or 'microsoft_Phi-4-reasoning' in full_model_name:
        return 'Phi-4-reasoning'
    else:
        return full_model_name.split('/')[-1] if '/' in full_model_name else full_model_name

def load_and_process_data():
    """Load and process all performance data."""
    data_dir = Path('8-14_data')
    platforms = ['qps_Vanilla', 'qps_BOOT', 'qps_IMA_W_Filter', 'qps_IMA_Wo_Filter']
    
    all_data = []
    
    for platform in platforms:
        platform_dir = data_dir / platform
        if not platform_dir.exists():
            continue
            
        # Get all detailed CSV files (exclude summary files)
        detailed_files = glob.glob(str(platform_dir / 'performance_detailed_*.csv'))
        # Filter out any summary files just to be sure
        detailed_files = [f for f in detailed_files if 'performance_summary' not in f]
        
        for detailed_file in detailed_files:
            # Extract metadata from filename
            filename = os.path.basename(detailed_file)
            parts = filename.replace('performance_detailed_', '').replace('.csv', '').split('_')
            
            if len(parts) >= 3:
                # Extract model and QPS from filename
                model_parts = []
                qps_value = None
                
                for i, part in enumerate(parts):
                    if part.endswith('qps'):
                        qps_value = int(part.replace('qps', ''))
                        model_parts = parts[:i]
                        break
                
                if qps_value and model_parts:
                    model_name = '_'.join(model_parts)
                    clean_model = extract_model_name(model_name)
                    
                    # Clean platform name - keep the correct 4 platforms
                    clean_platform = platform.replace('qps_', '')
                    if clean_platform == 'IMA_W_Filter':
                        clean_platform = 'IMA W/ Filter'
                    elif clean_platform == 'IMA_Wo_Filter':
                        clean_platform = 'IMA W/o Filter'
                    elif clean_platform == 'BOOT':
                        clean_platform = 'BOOT'
                    elif clean_platform == 'Vanilla':
                        clean_platform = 'Vanilla'
                    
                    # Load the detailed CSV and extract raw data
                    try:
                        raw_df = pd.read_csv(detailed_file)
                        
                        # Add metadata columns to each row
                        raw_df['platform'] = clean_platform
                        raw_df['model'] = clean_model
                        raw_df['qps'] = qps_value
                        raw_df['source_file'] = detailed_file
                        
                        all_data.append(raw_df)
                        
                    except Exception as e:
                        print(f"Error processing {detailed_file}: {e}")
                        continue
    
    if all_data:
        return pd.concat(all_data, ignore_index=True)
    else:
        return pd.DataFrame()

def export_data_by_model_metric(df):
    """Export data organized by model and metric for plotting scripts."""
    
    # Create export directory
    export_dir = Path('CDF_data_exports')
    export_dir.mkdir(exist_ok=True)
    
    models = sorted(df['model'].unique())
    metrics = ['ttft', 'throughput']
    platforms = ['IMA W/o Filter', 'BOOT', 'IMA W/ Filter', 'Vanilla']
    qps_values = sorted(df['qps'].unique())
    
    print(f"Exporting data for {len(models)} models, {len(metrics)} metrics...")
    
    for model in models:
        for metric in metrics:
            # Filter data for this model
            model_data = df[df['model'] == model]
            
            if model_data.empty or metric not in model_data.columns:
                continue
            
            # Create export data for this model-metric combination
            export_data = []
            
            for platform in platforms:
                for qps in qps_values:
                    subset = model_data[(model_data['platform'] == platform) & (model_data['qps'] == qps)]
                    
                    if not subset.empty and metric in subset.columns:
                        metric_values = subset[metric].dropna()
                        
                        if len(metric_values) > 0:
                            # Add each measurement as a row
                            for value in metric_values:
                                export_data.append({
                                    'model': model,
                                    'platform': platform,
                                    'qps': qps,
                                    'metric': metric,
                                    'value': value
                                })
            
            if export_data:
                # Save as CSV
                export_df = pd.DataFrame(export_data)
                filename = f'{model}_{metric}_raw_data.csv'
                filepath = export_dir / filename
                export_df.to_csv(filepath, index=False)
                print(f"Exported {len(export_df)} data points to {filepath}")

def export_summary_statistics(df):
    """Export summary statistics for each model-platform-QPS-metric combination."""
    
    export_dir = Path('CDF_data_exports')
    export_dir.mkdir(exist_ok=True)
    
    metrics = ['ttft', 'throughput']
    summary_data = []
    
    for metric in metrics:
        if metric not in df.columns:
            continue
            
        # Group by model, platform, QPS and calculate statistics
        grouped = df.groupby(['model', 'platform', 'qps'])[metric].agg([
            'count', 'mean', 'std', 'min', 'max',
            lambda x: np.percentile(x.dropna(), 50),  # p50
            lambda x: np.percentile(x.dropna(), 75),  # p75
            lambda x: np.percentile(x.dropna(), 90),  # p90
            lambda x: np.percentile(x.dropna(), 95),  # p95
            lambda x: np.percentile(x.dropna(), 99),  # p99
        ]).round(4)
        
        # Rename columns
        grouped.columns = ['count', 'mean', 'std', 'min', 'max', 'p50', 'p75', 'p90', 'p95', 'p99']
        
        # Reset index to make it a regular DataFrame
        grouped = grouped.reset_index()
        grouped['metric'] = metric
        
        summary_data.append(grouped)
    
    if summary_data:
        # Combine all metrics
        summary_df = pd.concat(summary_data, ignore_index=True)
        
        # Save summary statistics
        summary_filepath = export_dir / 'performance_summary_statistics.csv'
        summary_df.to_csv(summary_filepath, index=False)
        print(f"Exported summary statistics to {summary_filepath}")
        
        return summary_df
    
    return pd.DataFrame()

def main():
    """Main function to export CDF data."""
    print("Loading performance data from detailed CSV files...")
    
    # Load all raw data
    df = load_and_process_data()
    
    if df.empty:
        print("No data found! Please check the 8-14_data directory structure.")
        return
    
    print(f"Loaded {len(df)} raw data points")
    print(f"Platforms: {sorted(df['platform'].unique())}")
    print(f"Models: {sorted(df['model'].unique())}")
    print(f"QPS values: {sorted(df['qps'].unique())}")
    
    # Export raw data by model and metric
    export_data_by_model_metric(df)
    
    # Export summary statistics
    summary_df = export_summary_statistics(df)
    
    print(f"\nData export complete!")
    print(f"Files saved in: CDF_data_exports/")
    print(f"- Raw data files: {len(df['model'].unique()) * 2} files (per model-metric)")
    print(f"- Summary statistics: 1 file")

if __name__ == "__main__":
    main()
