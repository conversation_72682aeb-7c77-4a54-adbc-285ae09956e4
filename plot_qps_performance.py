#!/usr/bin/env python3
"""
QPS Performance Analysis and Plotting Script

This script analyzes performance data from the 8-14_data directory and creates
bar charts showing p50, p75, p90, p99 response times for each model and platform.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import glob
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def extract_model_name(full_model_name):
    """Extract a clean model name from the full model path."""
    # Handle different naming patterns from different platforms
    if 'Qwen3-8B' in full_model_name or 'Qwen_Qwen3-8B' in full_model_name:
        return 'Qwen3-8B'
    elif 'Qwen3-0.6B' in full_model_name or 'Qwen_Qwen3-0.6B' in full_model_name:
        return 'Qwen3-0.6B'
    elif 'DeepSeek-R1-Distill-Llama-8B' in full_model_name or 'deepseek-ai_DeepSeek-R1-Distill-Llama-8B' in full_model_name:
        return 'DeepSeek-R1-Llama-8B'
    elif 'DeepSeek-R1-Di<PERSON>ill-Qwen-14B' in full_model_name or 'deepseek-ai_DeepSeek-R1-Distill-Qwen-14B' in full_model_name:
        return 'DeepSeek-R1-Qwen-14B'
    elif 'Phi-4-reasoning' in full_model_name or 'microsoft_Phi-4-reasoning' in full_model_name:
        return 'Phi-4-reasoning'
    else:
        return full_model_name.split('/')[-1] if '/' in full_model_name else full_model_name

def calculate_percentiles_from_detailed(csv_file):
    """Calculate p50, p75, p90, p99 from detailed CSV file for multiple metrics."""
    try:
        df = pd.read_csv(csv_file)
        metrics = ['response_time', 'ttft', 'throughput']
        result = {}

        for metric in metrics:
            if metric in df.columns and len(df) > 0:
                values = df[metric].dropna()
                if len(values) > 0:
                    result[f'{metric}_p50'] = np.percentile(values, 50)
                    result[f'{metric}_p75'] = np.percentile(values, 75)
                    result[f'{metric}_p90'] = np.percentile(values, 90)
                    result[f'{metric}_p99'] = np.percentile(values, 99)

        return result if result else None
    except Exception as e:
        print(f"Error processing {csv_file}: {e}")
    return None

def load_and_process_data():
    """Load and process all performance data."""
    data_dir = Path('8-14_data')
    platforms = ['qps_Vanilla', 'qps_BOOT', 'qps_IMA_W_Filter', 'qps_IMA_Wo_Filter']

    all_data = []

    for platform in platforms:
        platform_dir = data_dir / platform
        if not platform_dir.exists():
            continue

        # Get all detailed CSV files (exclude summary files)
        detailed_files = glob.glob(str(platform_dir / 'performance_detailed_*.csv'))
        # Filter out any summary files just to be sure
        detailed_files = [f for f in detailed_files if 'performance_summary' not in f]

        for detailed_file in detailed_files:
            # Extract metadata from filename
            filename = os.path.basename(detailed_file)
            parts = filename.replace('performance_detailed_', '').replace('.csv', '').split('_')

            if len(parts) >= 3:
                # Extract model and QPS from filename
                model_parts = []
                qps_value = None

                for i, part in enumerate(parts):
                    if part.endswith('qps'):
                        qps_value = int(part.replace('qps', ''))
                        model_parts = parts[:i]
                        break

                if qps_value and model_parts:
                    model_name = '_'.join(model_parts)
                    clean_model = extract_model_name(model_name)

                    # Calculate percentiles from detailed data
                    percentiles = calculate_percentiles_from_detailed(detailed_file)

                    if percentiles:
                        # Clean platform name - keep the correct 4 platforms
                        clean_platform = platform.replace('qps_', '')
                        if clean_platform == 'IMA_W_Filter':
                            clean_platform = 'IMA W/ Filter'
                        elif clean_platform == 'IMA_Wo_Filter':
                            clean_platform = 'IMA W/o Filter'
                        elif clean_platform == 'BOOT':
                            clean_platform = 'BOOT'
                        elif clean_platform == 'Vanilla':
                            clean_platform = 'Vanilla'

                        data_row = {
                            'platform': clean_platform,
                            'model': clean_model,
                            'qps': qps_value,
                            'file': detailed_file
                        }

                        # Add all metric percentiles
                        for key, value in percentiles.items():
                            data_row[key] = value

                        all_data.append(data_row)

    return pd.DataFrame(all_data)

def calculate_cdf(data):
    """Calculate CDF values for given data."""
    sorted_data = np.sort(data)
    n = len(sorted_data)
    y_values = np.arange(1, n + 1) / n
    return sorted_data, y_values

def create_performance_plots(df):
    """Create separate CDF figures for each model and metric combination with QPS as different lines."""
    # Set up the plotting style
    plt.style.use('default')

    # Get unique models and QPS values
    models = sorted(df['model'].unique())
    qps_values = sorted(df['qps'].unique())

    # Define platform order - correct 4 platforms from 8-14_data
    platform_order = ['IMA W/o Filter', 'BOOT', 'IMA W/ Filter', 'Vanilla']
    platforms = [p for p in platform_order if p in df['platform'].unique()]

    metrics = ['ttft', 'throughput']

    # Metric display names and units
    metric_info = {
        'ttft': {'name': 'Time to First Token (TTFT)', 'unit': 'seconds', 'invert_better': False},
        'throughput': {'name': 'Throughput (TPS)', 'unit': 'tokens/second', 'invert_better': True}
    }

    # QPS colors and styles for CDF lines
    qps_styles = {
        2: {'color': '#1f77b4', 'linestyle': '-', 'linewidth': 2},
        4: {'color': '#ff7f0e', 'linestyle': '--', 'linewidth': 2},
        8: {'color': '#2ca02c', 'linestyle': '-.', 'linewidth': 2},
        16: {'color': '#d62728', 'linestyle': ':', 'linewidth': 3},
        32: {'color': '#9467bd', 'linestyle': '-', 'linewidth': 3}
    }

    # Create separate CDF figures for each model, metric, and platform combination
    for model in models:
        for metric in metrics:
            for platform in platforms:
                # Get all data for this model-metric-platform combination
                model_platform_data = df[(df['model'] == model) & (df['platform'] == platform)]

                if model_platform_data.empty:
                    continue

                fig, ax = plt.subplots(1, 1, figsize=(10, 6))

                # Plot CDF for each QPS level
                for qps in qps_values:
                    qps_data = model_platform_data[model_platform_data['qps'] == qps]

                    if not qps_data.empty:
                        # Load the original detailed CSV file to get raw data for CDF
                        csv_file = qps_data['file'].iloc[0]
                        try:
                            raw_df = pd.read_csv(csv_file)
                            if metric in raw_df.columns:
                                metric_values = raw_df[metric].dropna()

                                if len(metric_values) > 0:
                                    # Calculate CDF
                                    x_values, y_values = calculate_cdf(metric_values)

                                    style = qps_styles.get(qps, {'color': 'black', 'linestyle': '-', 'linewidth': 2})

                                    ax.plot(x_values, y_values,
                                           label=f'QPS {qps}',
                                           color=style['color'],
                                           linestyle=style['linestyle'],
                                           linewidth=style['linewidth'],
                                           alpha=0.8)
                        except Exception as e:
                            print(f"Error processing {csv_file}: {e}")
                            continue

                ax.set_xlabel(f'{metric_info[metric]["name"]} ({metric_info[metric]["unit"]})', fontsize=12)
                ax.set_ylabel('Cumulative Probability', fontsize=12)
                ax.set_title(f'{model} - {platform} - {metric_info[metric]["name"]} CDF', fontsize=14, fontweight='bold')
                ax.legend(fontsize=10)
                ax.grid(True, alpha=0.3)
                ax.set_ylim(0, 1)

                # Set x-axis to start from 0 for better comparison
                if not metric_info[metric]['invert_better']:
                    ax.set_xlim(left=0)

                plt.tight_layout()

                # Create folder for this metric if it doesn't exist
                metric_folder = Path(metric)
                metric_folder.mkdir(exist_ok=True)

                # Save as PDF in the appropriate folder
                filename = f'{model}_{platform}_{metric}_cdf.pdf'
                # Clean filename for filesystem
                filename = filename.replace('/', '_').replace(' ', '_')
                filepath = metric_folder / filename
                plt.savefig(filepath, format='pdf', dpi=300, bbox_inches='tight')
                print(f"Saved {filepath}")

                plt.close()

    return None

def main():
    """Main function to run the analysis."""
    print("Loading and processing QPS performance data from detailed CSV files only...")

    # Load data
    df = load_and_process_data()

    if df.empty:
        print("No data found! Please check the 8-14_data directory structure.")
        return

    print(f"Loaded {len(df)} data points from detailed CSV files")
    print(f"Platforms: {sorted(df['platform'].unique())}")
    print(f"Models: {sorted(df['model'].unique())}")
    print(f"QPS values: {sorted(df['qps'].unique())}")

    # Show which files were processed
    print(f"\nProcessed files from platforms:")
    for platform in sorted(df['platform'].unique()):
        platform_files = df[df['platform'] == platform]['file'].unique()
        print(f"  {platform}: {len(platform_files)} detailed CSV files")

    # Create plots
    print("\nCreating performance plots...")
    fig = create_performance_plots(df)
    
    # Create a summary table
    print("\n" + "="*80)
    print("PERFORMANCE SUMMARY TABLE")
    print("="*80)

    # Get all metric-percentile columns (only TTFT and throughput)
    metric_cols = [col for col in df.columns if any(col.startswith(f'{metric}_') for metric in ['ttft', 'throughput'])]

    if metric_cols:
        agg_dict = {col: 'mean' for col in metric_cols}
        summary_df = df.groupby(['platform', 'model', 'qps']).agg(agg_dict).round(3)
        print(summary_df.to_string())

    print(f"\nAll CDF plots saved as PDF files in organized folders:")
    print("  ttft/ - Time to First Token CDF charts")
    print("  throughput/ - Throughput (TPS) CDF charts")
    print("Each chart shows QPS levels as different colored lines")
    print("Analysis complete!")

if __name__ == "__main__":
    main()
