#!/usr/bin/env python3
"""
QPS Performance Analysis and Plotting Script

This script analyzes performance data from the 8-14_data directory and creates
bar charts showing p50, p75, p90, p99 response times for each model and platform.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import glob
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def extract_model_name(full_model_name):
    """Extract a clean model name from the full model path."""
    if 'Qwen3-8B' in full_model_name:
        return 'Qwen3-8B'
    elif 'Qwen3-0.6B' in full_model_name:
        return 'Qwen3-0.6B'
    elif 'DeepSeek-R1-Distill-Llama-8B' in full_model_name:
        return 'DeepSeek-R1-Llama-8B'
    elif 'DeepSeek-R1-Distill-Qwen-14B' in full_model_name:
        return 'DeepSeek-R1-Qwen-14B'
    elif 'Phi-4-reasoning' in full_model_name:
        return 'Phi-4-reasoning'
    else:
        return full_model_name.split('/')[-1] if '/' in full_model_name else full_model_name

def calculate_percentiles_from_detailed(csv_file):
    """Calculate p50, p75, p90, p99 from detailed CSV file for multiple metrics."""
    try:
        df = pd.read_csv(csv_file)
        metrics = ['response_time', 'ttft', 'throughput']
        result = {}

        for metric in metrics:
            if metric in df.columns and len(df) > 0:
                values = df[metric].dropna()
                if len(values) > 0:
                    result[f'{metric}_p50'] = np.percentile(values, 50)
                    result[f'{metric}_p75'] = np.percentile(values, 75)
                    result[f'{metric}_p90'] = np.percentile(values, 90)
                    result[f'{metric}_p99'] = np.percentile(values, 99)

        return result if result else None
    except Exception as e:
        print(f"Error processing {csv_file}: {e}")
    return None

def load_and_process_data():
    """Load and process all performance data."""
    data_dir = Path('8-14_data')
    platforms = ['qps_Vanilla', 'qps_BOOT', 'qps_IMA_W_Filter', 'qps_IMA_Wo_Filter']
    
    all_data = []
    
    for platform in platforms:
        platform_dir = data_dir / platform
        if not platform_dir.exists():
            continue
            
        # Get all detailed CSV files
        detailed_files = glob.glob(str(platform_dir / 'performance_detailed_*.csv'))
        
        for detailed_file in detailed_files:
            # Extract metadata from filename
            filename = os.path.basename(detailed_file)
            parts = filename.replace('performance_detailed_', '').replace('.csv', '').split('_')
            
            if len(parts) >= 3:
                # Extract model and QPS from filename
                model_parts = []
                qps_value = None
                
                for i, part in enumerate(parts):
                    if part.endswith('qps'):
                        qps_value = int(part.replace('qps', ''))
                        model_parts = parts[:i]
                        break
                
                if qps_value and model_parts:
                    model_name = '_'.join(model_parts)
                    clean_model = extract_model_name(model_name)
                    
                    # Calculate percentiles from detailed data
                    percentiles = calculate_percentiles_from_detailed(detailed_file)

                    if percentiles:
                        # Clean platform name
                        clean_platform = platform.replace('qps_', '')
                        if clean_platform == 'IMA_W_Filter':
                            clean_platform = 'IMA'
                        elif clean_platform == 'IMA_Wo_Filter':
                            clean_platform = 'non-TDX'
                        elif clean_platform == 'BOOT':
                            clean_platform = 'TDX'

                        data_row = {
                            'platform': clean_platform,
                            'model': clean_model,
                            'qps': qps_value,
                            'file': detailed_file
                        }

                        # Add all metric percentiles
                        for key, value in percentiles.items():
                            data_row[key] = value

                        all_data.append(data_row)
    
    return pd.DataFrame(all_data)

def create_performance_plots(df):
    """Create separate figures for each model and metric combination with QPS as x-axis."""
    # Set up the plotting style
    plt.style.use('default')

    # Get unique models and QPS values
    models = sorted(df['model'].unique())
    qps_values = sorted(df['qps'].unique())

    # Define platform order based on user preference: non-TDX, TDX, IMA
    platform_order = ['non-TDX', 'TDX', 'IMA', 'Vanilla']
    platforms = [p for p in platform_order if p in df['platform'].unique()]

    percentiles = ['p50', 'p75', 'p90', 'p99']
    metrics = ['response_time', 'ttft', 'throughput']

    # Metric display names and units
    metric_info = {
        'response_time': {'name': 'Response Time', 'unit': 'seconds', 'invert_better': False},
        'ttft': {'name': 'Time to First Token (TTFT)', 'unit': 'seconds', 'invert_better': False},
        'throughput': {'name': 'Throughput', 'unit': 'tokens/second', 'invert_better': True}
    }

    # Platform colors and markers
    platform_styles = {
        'non-TDX': {'color': '#1f77b4', 'marker': 'o', 'linestyle': '-', 'linewidth': 2},
        'TDX': {'color': '#ff7f0e', 'marker': 's', 'linestyle': '--', 'linewidth': 2},
        'IMA': {'color': '#2ca02c', 'marker': '^', 'linestyle': '-.', 'linewidth': 2},
        'Vanilla': {'color': '#d62728', 'marker': 'D', 'linestyle': ':', 'linewidth': 3}
    }

    # Create separate figures for each model, metric, and percentile combination
    for model in models:
        for metric in metrics:
            for percentile in percentiles:
                metric_col = f'{metric}_{percentile}'

                # Check if this metric-percentile combination exists in the data
                if metric_col not in df.columns:
                    continue

                fig, ax = plt.subplots(1, 1, figsize=(10, 6))

                # Plot each platform for this model
                for platform in platforms:
                    model_platform_data = df[(df['model'] == model) & (df['platform'] == platform)]

                    if not model_platform_data.empty and not model_platform_data[metric_col].isna().all():
                        # Sort by QPS for proper line plotting
                        model_platform_data = model_platform_data.sort_values('qps')

                        style = platform_styles.get(platform, {'color': 'black', 'marker': 'o', 'linestyle': '-', 'linewidth': 2})

                        ax.plot(model_platform_data['qps'], model_platform_data[metric_col],
                               label=f'{platform}',
                               color=style['color'],
                               marker=style['marker'],
                               linestyle=style['linestyle'],
                               linewidth=style['linewidth'],
                               markersize=8,
                               alpha=0.8)

                ax.set_xlabel('QPS (Queries Per Second)', fontsize=12)
                ax.set_ylabel(f'{percentile.upper()} {metric_info[metric]["name"]} ({metric_info[metric]["unit"]})', fontsize=12)
                ax.set_title(f'{model} - {percentile.upper()} {metric_info[metric]["name"]} vs QPS', fontsize=14, fontweight='bold')
                ax.legend(fontsize=10)
                ax.grid(True, alpha=0.3)
                ax.set_xticks(qps_values)

                # Set y-axis to start from 0 for better comparison (except for throughput which might benefit from auto-scaling)
                if not metric_info[metric]['invert_better']:
                    ax.set_ylim(bottom=0)

                plt.tight_layout()

                # Save as PDF
                filename = f'{model}_{metric}_{percentile}.pdf'
                plt.savefig(filename, format='pdf', dpi=300, bbox_inches='tight')
                print(f"Saved {filename}")

                plt.close()

    return None

def main():
    """Main function to run the analysis."""
    print("Loading and processing QPS performance data...")
    
    # Load data
    df = load_and_process_data()
    
    if df.empty:
        print("No data found! Please check the 8-14_data directory structure.")
        return
    
    print(f"Loaded {len(df)} data points")
    print(f"Platforms: {sorted(df['platform'].unique())}")
    print(f"Models: {sorted(df['model'].unique())}")
    print(f"QPS values: {sorted(df['qps'].unique())}")
    
    # Create plots
    print("\nCreating performance plots...")
    fig = create_performance_plots(df)
    
    # Create a summary table
    print("\n" + "="*80)
    print("PERFORMANCE SUMMARY TABLE")
    print("="*80)

    # Get all metric-percentile columns
    metric_cols = [col for col in df.columns if any(col.startswith(f'{metric}_') for metric in ['response_time', 'ttft', 'throughput'])]

    if metric_cols:
        agg_dict = {col: 'mean' for col in metric_cols}
        summary_df = df.groupby(['platform', 'model', 'qps']).agg(agg_dict).round(3)
        print(summary_df.to_string())

    print(f"\nAll model-metric-percentile plots saved as PDF files")
    print("Analysis complete!")

if __name__ == "__main__":
    main()
