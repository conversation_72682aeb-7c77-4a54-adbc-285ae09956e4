#!/usr/bin/env python3
"""
QPS Performance Analysis and Plotting Script

This script analyzes performance data from the 8-14_data directory and creates
bar charts showing p50, p75, p90, p99 response times for each model and platform.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import glob
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def extract_model_name(full_model_name):
    """Extract a clean model name from the full model path."""
    if 'Qwen3-8B' in full_model_name:
        return 'Qwen3-8B'
    elif 'Qwen3-0.6B' in full_model_name:
        return 'Qwen3-0.6B'
    elif 'DeepSeek-R1-Distill-Llama-8B' in full_model_name:
        return 'DeepSeek-R1-Llama-8B'
    elif 'DeepSeek-R1-Distill-Qwen-14B' in full_model_name:
        return 'DeepSeek-R1-Qwen-14B'
    elif 'Phi-4-reasoning' in full_model_name:
        return 'Phi-4-reasoning'
    else:
        return full_model_name.split('/')[-1] if '/' in full_model_name else full_model_name

def calculate_percentiles_from_detailed(csv_file):
    """Calculate p50, p75, p90, p99 from detailed CSV file."""
    try:
        df = pd.read_csv(csv_file)
        if 'response_time' in df.columns and len(df) > 0:
            response_times = df['response_time'].dropna()
            if len(response_times) > 0:
                return {
                    'p50': np.percentile(response_times, 50),
                    'p75': np.percentile(response_times, 75),
                    'p90': np.percentile(response_times, 90),
                    'p99': np.percentile(response_times, 99)
                }
    except Exception as e:
        print(f"Error processing {csv_file}: {e}")
    return None

def load_and_process_data():
    """Load and process all performance data."""
    data_dir = Path('8-14_data')
    platforms = ['qps_Vanilla', 'qps_BOOT', 'qps_IMA_W_Filter', 'qps_IMA_Wo_Filter']
    
    all_data = []
    
    for platform in platforms:
        platform_dir = data_dir / platform
        if not platform_dir.exists():
            continue
            
        # Get all detailed CSV files
        detailed_files = glob.glob(str(platform_dir / 'performance_detailed_*.csv'))
        
        for detailed_file in detailed_files:
            # Extract metadata from filename
            filename = os.path.basename(detailed_file)
            parts = filename.replace('performance_detailed_', '').replace('.csv', '').split('_')
            
            if len(parts) >= 3:
                # Extract model and QPS from filename
                model_parts = []
                qps_value = None
                
                for i, part in enumerate(parts):
                    if part.endswith('qps'):
                        qps_value = int(part.replace('qps', ''))
                        model_parts = parts[:i]
                        break
                
                if qps_value and model_parts:
                    model_name = '_'.join(model_parts)
                    clean_model = extract_model_name(model_name)
                    
                    # Calculate percentiles from detailed data
                    percentiles = calculate_percentiles_from_detailed(detailed_file)
                    
                    if percentiles:
                        # Clean platform name
                        clean_platform = platform.replace('qps_', '')
                        if clean_platform == 'IMA_W_Filter':
                            clean_platform = 'IMA'
                        elif clean_platform == 'IMA_Wo_Filter':
                            clean_platform = 'non-TDX'
                        elif clean_platform == 'BOOT':
                            clean_platform = 'TDX'
                        
                        data_row = {
                            'platform': clean_platform,
                            'model': clean_model,
                            'qps': qps_value,
                            'p50': percentiles['p50'],
                            'p75': percentiles['p75'],
                            'p90': percentiles['p90'],
                            'p99': percentiles['p99'],
                            'file': detailed_file
                        }
                        all_data.append(data_row)
    
    return pd.DataFrame(all_data)

def create_performance_plots(df):
    """Create line charts for performance data with models as subplots and QPS as x-axis."""
    # Set up the plotting style
    plt.style.use('default')

    # Get unique models and QPS values
    models = sorted(df['model'].unique())
    qps_values = sorted(df['qps'].unique())

    # Define platform order based on user preference: non-TDX, TDX, IMA
    platform_order = ['non-TDX', 'TDX', 'IMA', 'Vanilla']
    platforms = [p for p in platform_order if p in df['platform'].unique()]

    # Create subplots for each model
    n_models = len(models)
    n_cols = 3
    n_rows = (n_models + n_cols - 1) // n_cols

    fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6 * n_rows))
    if n_rows == 1:
        axes = [axes] if n_models == 1 else axes
    else:
        axes = axes.flatten()

    percentiles = ['p50', 'p75', 'p90', 'p99']
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']  # Blue, Orange, Green, Red

    # Platform colors and markers
    platform_styles = {
        'non-TDX': {'color': '#1f77b4', 'marker': 'o', 'linestyle': '-'},
        'TDX': {'color': '#ff7f0e', 'marker': 's', 'linestyle': '--'},
        'IMA': {'color': '#2ca02c', 'marker': '^', 'linestyle': '-.'},
        'Vanilla': {'color': '#d62728', 'marker': 'D', 'linestyle': ':'}
    }

    for idx, model in enumerate(models):
        if idx >= len(axes):
            break

        ax = axes[idx]
        model_data = df[df['model'] == model]

        if model_data.empty:
            ax.set_title(f'{model} - No Data')
            continue

        # Plot each percentile for each platform
        for percentile in percentiles:
            for platform in platforms:
                platform_data = model_data[model_data['platform'] == platform]
                if not platform_data.empty:
                    # Sort by QPS for proper line plotting
                    platform_data = platform_data.sort_values('qps')

                    style = platform_styles.get(platform, {'color': 'black', 'marker': 'o', 'linestyle': '-'})

                    ax.plot(platform_data['qps'], platform_data[percentile],
                           label=f'{platform} {percentile.upper()}',
                           color=style['color'],
                           marker=style['marker'],
                           linestyle=style['linestyle'],
                           linewidth=2,
                           markersize=6,
                           alpha=0.8)

        ax.set_xlabel('QPS (Queries Per Second)')
        ax.set_ylabel('Response Time (seconds)')
        ax.set_title(f'{model} - Performance by QPS')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
        ax.set_xticks(qps_values)

        # Set y-axis to start from 0 for better comparison
        ax.set_ylim(bottom=0)

    # Hide unused subplots
    for idx in range(len(models), len(axes)):
        axes[idx].set_visible(False)

    plt.tight_layout()
    plt.savefig('qps_performance_by_model.png', dpi=300, bbox_inches='tight')
    plt.show()

    return fig

def main():
    """Main function to run the analysis."""
    print("Loading and processing QPS performance data...")
    
    # Load data
    df = load_and_process_data()
    
    if df.empty:
        print("No data found! Please check the 8-14_data directory structure.")
        return
    
    print(f"Loaded {len(df)} data points")
    print(f"Platforms: {sorted(df['platform'].unique())}")
    print(f"Models: {sorted(df['model'].unique())}")
    print(f"QPS values: {sorted(df['qps'].unique())}")
    
    # Create plots
    print("\nCreating performance plots...")
    fig = create_performance_plots(df)
    
    # Create a summary table
    print("\n" + "="*80)
    print("PERFORMANCE SUMMARY TABLE")
    print("="*80)

    summary_df = df.groupby(['platform', 'model', 'qps']).agg({
        'p50': 'mean',
        'p75': 'mean',
        'p90': 'mean',
        'p99': 'mean'
    }).round(3)

    print(summary_df.to_string())

    print(f"\nPlot saved as 'qps_performance_by_model.png'")
    print("Analysis complete!")

if __name__ == "__main__":
    main()
